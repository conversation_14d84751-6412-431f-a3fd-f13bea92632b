/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_err.h>

// Note: We don't include Tongsuo headers directly to avoid conflicts with <PERSON><PERSON><PERSON><PERSON>
// Instead, we use extern declarations for the functions we need

// Declare Tongsuo stub functions
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

static void tongsuo_init_test(void) {
    int result = TONGSUO_init();
    printf("Tongsuo init result: %d\n", result);
    if (result != 1) {
        printf("ERROR: Tongsuo init failed\n");
    } else {
        printf("SUCCESS: Tongsuo init passed\n");
    }
}

static void tongsuo_version_test(void) {
    const char* version = TONGSUO_version_text();
    printf("Tongsuo version: %s\n", version ? version : "NULL");
    if (!version) {
        printf("ERROR: Tongsuo version is NULL\n");
    } else {
        printf("SUCCESS: Tongsuo version test passed\n");
    }
}

static void tongsuo_cleanup_test(void) {
    TONGSUO_cleanup();
    printf("SUCCESS: Tongsuo cleanup completed\n");
}

int main(void) {
    printf("=== Tongsuo Test Suite ===\n");

    tongsuo_init_test();
    tongsuo_version_test();
    tongsuo_cleanup_test();

    printf("=== All tests completed ===\n");
    return 0;
}
