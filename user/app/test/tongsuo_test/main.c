/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <trusty_unittest.h>

// Note: We don't include Tongsuo headers directly to avoid conflicts with BoringSSL
// Instead, we use extern declarations for the functions we need

// Declare Tongsuo stub functions
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

// Define test fixture type
typedef struct tongsuo_test {
} tongsuo_test_t;

TEST_F_SETUP(tongsuo_test) {
}

TEST_F_TEARDOWN(tongsuo_test) {
}

TEST_F(tongsuo_test, init_test) {
    int result = TONGSUO_init();
    EXPECT_EQ(1, result, "Tongsuo init should return 1");
test_abort:;
}

TEST_F(tongsuo_test, version_test) {
    const char* version = TONGSUO_version_text();
    EXPECT_NE(NULL, version, "Tongsuo version should not be NULL");
    if (version) {
        printf("Tongsuo version: %s\n", version);
    }
test_abort:;
}

TEST_F(tongsuo_test, cleanup_test) {
    TONGSUO_cleanup();
    // If we reach here, cleanup didn't crash
    EXPECT_EQ(1, 1, "Tongsuo cleanup should complete without error");
test_abort:;
}

PORT_TEST(tongsuo_test, "com.android.tongsuo-test");
