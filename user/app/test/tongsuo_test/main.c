/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_err.h>
#include <lib/unittest/unittest.h>

// Note: We don't include Tongsuo headers directly to avoid conflicts with <PERSON>ringSSL
// Instead, we use extern declarations for the functions we need

// Declare Tongsuo stub functions
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

TEST_F_SETUP(tongsuo_test) {
test_abort:;
}

TEST_F_TEARDOWN(tongsuo_test) {
test_abort:;
}

TEST_F(tongsuo_test, init_test) {
    int result = TONGSUO_init();
    EXPECT_EQ(1, result, "Tongsuo init should return 1");
test_abort:;
}

TEST_F(tongsuo_test, version_test) {
    const char* version = TONGSUO_version_text();
    EXPECT_NE(NULL, version, "Tongsuo version should not be NULL");
    if (version) {
        printf("Tongsuo version: %s\n", version);
    }
test_abort:;
}

TEST_F(tongsuo_test, cleanup_test) {
    TONGSUO_cleanup();
    // If we reach here, cleanup didn't crash
    EXPECT_EQ(1, 1, "Tongsuo cleanup should complete without error");
test_abort:;
}

PORT_TEST(tongsuo_test, "com.android.trusty.tongsuo_test");
