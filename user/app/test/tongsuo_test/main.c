/*
 * Copyright (C) 2024 The Tongsuo Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <trusty_unittest.h>

// Include Tongsuo headers directly (now exported globally)
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/err.h>
// Tongsuo specific headers
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>

// Tongsuo functions are now available through headers

// Define test fixture type
typedef struct tongsuo_test {
} tongsuo_test_t;

TEST_F_SETUP(tongsuo_test) {
}

TEST_F_TEARDOWN(tongsuo_test) {
}

TEST_F(tongsuo_test, init_test) {
    // Test OpenSSL/Tongsuo initialization
    int result = OPENSSL_init_crypto(OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
    EXPECT_EQ(1, result, "OpenSSL/Tongsuo init should return 1");
test_abort:;
}

TEST_F(tongsuo_test, version_test) {
    const char* version = OpenSSL_version(OPENSSL_VERSION);
    EXPECT_NE(NULL, version, "OpenSSL/Tongsuo version should not be NULL");
    if (version) {
        printf("OpenSSL/Tongsuo version: %s\n", version);
    }
test_abort:;
}

TEST_F(tongsuo_test, cleanup_test) {
    OPENSSL_cleanup();
    // If we reach here, cleanup didn't crash
    EXPECT_EQ(1, 1, "OpenSSL/Tongsuo cleanup should complete without error");
test_abort:;
}

PORT_TEST(tongsuo_test, "com.android.tongsuo-test");
