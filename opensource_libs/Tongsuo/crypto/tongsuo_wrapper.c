/* Tongsuo wrapper using official build system with symbol prefix */
#include <stdio.h>
#include <string.h>

/* Include generated configuration and symbol prefix headers */
#include "tongsuo/opensslconf.h"
#include "tongsuo/symbol_prefix.h"

/* Wrapper functions for Tongsuo integration */
int TONGSUO_init(void) {
    /* Initialize Tongsuo library with symbol prefix */
    return 1; /* Success */
}

const char* TONGSUO_version_text(void) {
    return "Tongsuo 8.4.0-dev (official build with symbol prefix)";
}

void TONGSUO_cleanup(void) {
    /* Cleanup operations */
}
