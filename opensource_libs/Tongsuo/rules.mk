# Copyright (C) 2024 Tongsuo Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Tongsuo library integration for Trusty TEE
# This file integrates Tongsuo using a simple stub approach with namespace isolation

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Module dependencies
MODULE_DEPS += \
    user/base/lib/rng

# Create a simple stub implementation for now
# This demonstrates the namespace isolation concept
MODULE_SRCS += \
    $(LOCAL_DIR)/crypto/tongsuo_stub.c

# IMPORTANT: Namespace isolation
# Tongsuo headers are NOT exported globally to avoid conflicts with BoringSSL
# Only modules that explicitly depend on Tongsuo will include its headers
# This is achieved by NOT setting MODULE_EXPORT_INCLUDES

include make/rctee_lib.mk
