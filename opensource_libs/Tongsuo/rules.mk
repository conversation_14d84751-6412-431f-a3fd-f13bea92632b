# Copyright (C) 2024 The Tongsuo Project.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
# OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
# CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

# This file is used only by Trusty to integrate Tongsuo cryptographic library.

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Create a simple stub implementation for now
# This provides basic integration without full Tongsuo compilation

# Create stub source file
TONGSUO_STUB_SRC := $(LOCAL_DIR)/crypto/tongsuo_stub.c

$(TONGSUO_STUB_SRC): | $(dir $(TONGSUO_STUB_SRC))
	@echo "Creating Tongsuo stub implementation..."
	@echo '/* Tongsuo stub implementation for Trusty */' > $@
	@echo '#include <stddef.h>' >> $@
	@echo '#include <stdint.h>' >> $@
	@echo '' >> $@
	@echo '/* Stub functions for basic Tongsuo integration */' >> $@
	@echo 'int TONGSUO_init(void) { return 1; }' >> $@
	@echo 'void TONGSUO_cleanup(void) { }' >> $@
	@echo 'const char* TONGSUO_version_text(void) { return "Tongsuo 8.4.0-dev"; }' >> $@

$(dir $(TONGSUO_STUB_SRC)):
	@mkdir -p $@

# Module sources
MODULE_SRCS += $(TONGSUO_STUB_SRC)

# Do not export headers globally to avoid conflicts with BoringSSL
# MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Tongsuo specific defines for namespace isolation
MODULE_CFLAGS += -DTONGSUO_IMPLEMENTATION
MODULE_CFLAGS += -DOPENSSL_SMALL -DOPENSSL_NO_ASM
MODULE_CFLAGS += -DOPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED

# The AOSP stdatomic.h clang header does not build against musl. Disable C11
# atomics.
MODULE_CFLAGS += -D__STDC_NO_ATOMICS__

# Trusty specific flags
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__

# Include openssl-stubs for compatibility
include user/base/lib/openssl-stubs/openssl-stubs-inc.mk

include make/rctee_lib.mk
