# Copyright (C) 2024 The Tongsuo Project.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
# OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
# CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

# Tongsuo cryptographic library integration for Trusty
# Using symbol-prefix approach to avoid conflicts with BoringSSL

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Tongsuo symbol prefix for namespace isolation
TONGSUO_PREFIX := TONGSUO_

# Tongsuo configuration flags for Trusty environment
MODULE_CFLAGS += \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_ASYNC \
	-DOPENSSL_NO_DEPRECATED \
	-DOPENSSL_NO_DYNAMIC_ENGINE \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_APPS \
	-DOPENSSL_NO_UI_CONSOLE \
	-DOPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED \
	-DOPENSSL_SMALL \
	-DOPENSSL_NO_ASM \
	-D__STDC_NO_ATOMICS__ \
	-D__linux__ \
	-D__TRUSTY__ \
	-DTONGSUO_SYMBOL_PREFIX=$(TONGSUO_PREFIX) \

# Enable SM algorithms
MODULE_CFLAGS += \
	-DOPENSSL_SM2_THRESHOLD \
	-DOPENSSL_SM3 \
	-DOPENSSL_SM4 \

# Include Tongsuo source files
include $(LOCAL_DIR)/sources.mk

MODULE_LIBRARY_DEPS += \
	user/base/lib/libc-rctee \

# Do not export headers globally to avoid conflicts with BoringSSL
# Modules that need Tongsuo should explicitly include the path
# MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

include make/library.mk
