# Copyright (C) 2024 The Tongsuo Project.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
# OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
# CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

# Tongsuo source files for trusty-tee integration
# This file contains a minimal set of Tongsuo sources for basic functionality

# Core crypto sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/cryptlib.c \
	$(LOCAL_DIR)/crypto/mem.c \
	$(LOCAL_DIR)/crypto/mem_clr.c \
	$(LOCAL_DIR)/crypto/mem_sec.c \
	$(LOCAL_DIR)/crypto/init.c \
	$(LOCAL_DIR)/crypto/ex_data.c \
	$(LOCAL_DIR)/crypto/cpuid.c \
	$(LOCAL_DIR)/crypto/ctype.c \
	$(LOCAL_DIR)/crypto/threads_none.c \
	$(LOCAL_DIR)/crypto/getenv.c \
	$(LOCAL_DIR)/crypto/o_init.c \
	$(LOCAL_DIR)/crypto/o_str.c \
	$(LOCAL_DIR)/crypto/o_time.c \
	$(LOCAL_DIR)/crypto/cpt_err.c \
	$(LOCAL_DIR)/crypto/ebcdic.c \
	$(LOCAL_DIR)/crypto/uid.c \
	$(LOCAL_DIR)/crypto/context.c \
	$(LOCAL_DIR)/crypto/sparse_array.c \
	$(LOCAL_DIR)/crypto/asn1_dsa.c \
	$(LOCAL_DIR)/crypto/packet.c \
	$(LOCAL_DIR)/crypto/param_build.c \
	$(LOCAL_DIR)/crypto/param_build_set.c \
	$(LOCAL_DIR)/crypto/params.c \
	$(LOCAL_DIR)/crypto/params_dup.c \
	$(LOCAL_DIR)/crypto/params_from_text.c \
	$(LOCAL_DIR)/crypto/bsearch.c \
	$(LOCAL_DIR)/crypto/provider.c \
	$(LOCAL_DIR)/crypto/provider_child.c \
	$(LOCAL_DIR)/crypto/provider_conf.c \
	$(LOCAL_DIR)/crypto/provider_core.c \
	$(LOCAL_DIR)/crypto/provider_predefined.c \
	$(LOCAL_DIR)/crypto/core_algorithm.c \
	$(LOCAL_DIR)/crypto/core_fetch.c \
	$(LOCAL_DIR)/crypto/core_namemap.c \
	$(LOCAL_DIR)/crypto/self_test_core.c \
	$(LOCAL_DIR)/crypto/info.c \
	$(LOCAL_DIR)/crypto/initthread.c \
	$(LOCAL_DIR)/crypto/trace.c \
	$(LOCAL_DIR)/crypto/der_writer.c \
	$(LOCAL_DIR)/crypto/passphrase.c \
	$(LOCAL_DIR)/crypto/punycode.c \

# BN (Big Number) sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/bn/bn_add.c \
	$(LOCAL_DIR)/crypto/bn/bn_asm.c \
	$(LOCAL_DIR)/crypto/bn/bn_blind.c \
	$(LOCAL_DIR)/crypto/bn/bn_const.c \
	$(LOCAL_DIR)/crypto/bn/bn_conv.c \
	$(LOCAL_DIR)/crypto/bn/bn_ctx.c \
	$(LOCAL_DIR)/crypto/bn/bn_dh.c \
	$(LOCAL_DIR)/crypto/bn/bn_div.c \
	$(LOCAL_DIR)/crypto/bn/bn_err.c \
	$(LOCAL_DIR)/crypto/bn/bn_exp.c \
	$(LOCAL_DIR)/crypto/bn/bn_exp2.c \
	$(LOCAL_DIR)/crypto/bn/bn_gcd.c \
	$(LOCAL_DIR)/crypto/bn/bn_gf2m.c \
	$(LOCAL_DIR)/crypto/bn/bn_intern.c \
	$(LOCAL_DIR)/crypto/bn/bn_kron.c \
	$(LOCAL_DIR)/crypto/bn/bn_lib.c \
	$(LOCAL_DIR)/crypto/bn/bn_mod.c \
	$(LOCAL_DIR)/crypto/bn/bn_mont.c \
	$(LOCAL_DIR)/crypto/bn/bn_mpi.c \
	$(LOCAL_DIR)/crypto/bn/bn_mul.c \
	$(LOCAL_DIR)/crypto/bn/bn_nist.c \
	$(LOCAL_DIR)/crypto/bn/bn_prime.c \
	$(LOCAL_DIR)/crypto/bn/bn_print.c \
	$(LOCAL_DIR)/crypto/bn/bn_rand.c \
	$(LOCAL_DIR)/crypto/bn/bn_recp.c \
	$(LOCAL_DIR)/crypto/bn/bn_rsa_fips186_4.c \
	$(LOCAL_DIR)/crypto/bn/bn_shift.c \
	$(LOCAL_DIR)/crypto/bn/bn_sm2.c \
	$(LOCAL_DIR)/crypto/bn/bn_sqr.c \
	$(LOCAL_DIR)/crypto/bn/bn_sqrt.c \
	$(LOCAL_DIR)/crypto/bn/bn_srp.c \
	$(LOCAL_DIR)/crypto/bn/bn_word.c \
	$(LOCAL_DIR)/crypto/bn/bn_x931p.c \

# Buffer sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/buffer/buffer.c \
	$(LOCAL_DIR)/crypto/buffer/buf_err.c \

# Stack sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/stack/stack.c \

# LHash sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/lhash/lhash.c \

# Random sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/rand/rand_lib.c \
	$(LOCAL_DIR)/crypto/rand/rand_err.c \
	$(LOCAL_DIR)/crypto/rand/randfile.c \
	$(LOCAL_DIR)/crypto/rand/rand_deprecated.c \
	$(LOCAL_DIR)/crypto/rand/rand_pool.c \

# Error handling sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/err/err.c \
	$(LOCAL_DIR)/crypto/err/err_all.c \
	$(LOCAL_DIR)/crypto/err/err_all_legacy.c \
	$(LOCAL_DIR)/crypto/err/err_blocks.c \

# Objects sources
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/objects/obj_dat.c \
	$(LOCAL_DIR)/crypto/objects/obj_err.c \
	$(LOCAL_DIR)/crypto/objects/obj_lib.c \
	$(LOCAL_DIR)/crypto/objects/obj_xref.c \
	$(LOCAL_DIR)/crypto/objects/o_names.c \

# EVP sources (basic)
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/evp/digest.c \
	$(LOCAL_DIR)/crypto/evp/evp_enc.c \
	$(LOCAL_DIR)/crypto/evp/evp_err.c \
	$(LOCAL_DIR)/crypto/evp/evp_fetch.c \
	$(LOCAL_DIR)/crypto/evp/evp_key.c \
	$(LOCAL_DIR)/crypto/evp/evp_lib.c \
	$(LOCAL_DIR)/crypto/evp/evp_pkey.c \
	$(LOCAL_DIR)/crypto/evp/evp_utils.c \
	$(LOCAL_DIR)/crypto/evp/exchange.c \
	$(LOCAL_DIR)/crypto/evp/keymgmt_lib.c \
	$(LOCAL_DIR)/crypto/evp/keymgmt_meth.c \
	$(LOCAL_DIR)/crypto/evp/m_null.c \
	$(LOCAL_DIR)/crypto/evp/names.c \
	$(LOCAL_DIR)/crypto/evp/p_lib.c \
	$(LOCAL_DIR)/crypto/evp/pmeth_lib.c \
	$(LOCAL_DIR)/crypto/evp/signature.c \

# ASN1 sources (basic)
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/asn1/a_bitstr.c \
	$(LOCAL_DIR)/crypto/asn1/a_d2i_fp.c \
	$(LOCAL_DIR)/crypto/asn1/a_digest.c \
	$(LOCAL_DIR)/crypto/asn1/a_dup.c \
	$(LOCAL_DIR)/crypto/asn1/a_gentm.c \
	$(LOCAL_DIR)/crypto/asn1/a_i2d_fp.c \
	$(LOCAL_DIR)/crypto/asn1/a_int.c \
	$(LOCAL_DIR)/crypto/asn1/a_mbstr.c \
	$(LOCAL_DIR)/crypto/asn1/a_object.c \
	$(LOCAL_DIR)/crypto/asn1/a_octet.c \
	$(LOCAL_DIR)/crypto/asn1/a_print.c \
	$(LOCAL_DIR)/crypto/asn1/a_sign.c \
	$(LOCAL_DIR)/crypto/asn1/a_strex.c \
	$(LOCAL_DIR)/crypto/asn1/a_strnid.c \
	$(LOCAL_DIR)/crypto/asn1/a_time.c \
	$(LOCAL_DIR)/crypto/asn1/a_type.c \
	$(LOCAL_DIR)/crypto/asn1/a_utctm.c \
	$(LOCAL_DIR)/crypto/asn1/a_utf8.c \
	$(LOCAL_DIR)/crypto/asn1/a_verify.c \
	$(LOCAL_DIR)/crypto/asn1/ameth_lib.c \
	$(LOCAL_DIR)/crypto/asn1/asn1_err.c \
	$(LOCAL_DIR)/crypto/asn1/asn1_gen.c \
	$(LOCAL_DIR)/crypto/asn1/asn1_item_list.c \
	$(LOCAL_DIR)/crypto/asn1/asn1_lib.c \
	$(LOCAL_DIR)/crypto/asn1/asn1_parse.c \
	$(LOCAL_DIR)/crypto/asn1/asn_mime.c \
	$(LOCAL_DIR)/crypto/asn1/asn_moid.c \
	$(LOCAL_DIR)/crypto/asn1/asn_pack.c \
	$(LOCAL_DIR)/crypto/asn1/bio_asn1.c \
	$(LOCAL_DIR)/crypto/asn1/bio_ndef.c \
	$(LOCAL_DIR)/crypto/asn1/d2i_param.c \
	$(LOCAL_DIR)/crypto/asn1/d2i_pr.c \
	$(LOCAL_DIR)/crypto/asn1/d2i_pu.c \
	$(LOCAL_DIR)/crypto/asn1/evp_asn1.c \
	$(LOCAL_DIR)/crypto/asn1/f_int.c \
	$(LOCAL_DIR)/crypto/asn1/f_string.c \
	$(LOCAL_DIR)/crypto/asn1/i2d_evp.c \
	$(LOCAL_DIR)/crypto/asn1/nsseq.c \
	$(LOCAL_DIR)/crypto/asn1/p5_pbe.c \
	$(LOCAL_DIR)/crypto/asn1/p5_pbev2.c \
	$(LOCAL_DIR)/crypto/asn1/p5_scrypt.c \
	$(LOCAL_DIR)/crypto/asn1/p8_pkey.c \
	$(LOCAL_DIR)/crypto/asn1/t_bitst.c \
	$(LOCAL_DIR)/crypto/asn1/t_pkey.c \
	$(LOCAL_DIR)/crypto/asn1/t_spki.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_dec.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_enc.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_fre.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_new.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_prn.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_scn.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_typ.c \
	$(LOCAL_DIR)/crypto/asn1/tasn_utl.c \
	$(LOCAL_DIR)/crypto/asn1/x_algor.c \
	$(LOCAL_DIR)/crypto/asn1/x_bignum.c \
	$(LOCAL_DIR)/crypto/asn1/x_info.c \
	$(LOCAL_DIR)/crypto/asn1/x_int64.c \
	$(LOCAL_DIR)/crypto/asn1/x_long.c \
	$(LOCAL_DIR)/crypto/asn1/x_pkey.c \
	$(LOCAL_DIR)/crypto/asn1/x_sig.c \
	$(LOCAL_DIR)/crypto/asn1/x_spki.c \
	$(LOCAL_DIR)/crypto/asn1/x_val.c \

# BIO sources (basic)
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/bio/bio_addr.c \
	$(LOCAL_DIR)/crypto/bio/bio_cb.c \
	$(LOCAL_DIR)/crypto/bio/bio_dump.c \
	$(LOCAL_DIR)/crypto/bio/bio_err.c \
	$(LOCAL_DIR)/crypto/bio/bio_lib.c \
	$(LOCAL_DIR)/crypto/bio/bio_meth.c \
	$(LOCAL_DIR)/crypto/bio/bio_print.c \
	$(LOCAL_DIR)/crypto/bio/bio_sock.c \
	$(LOCAL_DIR)/crypto/bio/bio_sock2.c \
	$(LOCAL_DIR)/crypto/bio/bss_bio.c \
	$(LOCAL_DIR)/crypto/bio/bss_core.c \
	$(LOCAL_DIR)/crypto/bio/bss_fd.c \
	$(LOCAL_DIR)/crypto/bio/bss_file.c \
	$(LOCAL_DIR)/crypto/bio/bss_log.c \
	$(LOCAL_DIR)/crypto/bio/bss_mem.c \
	$(LOCAL_DIR)/crypto/bio/bss_null.c \
	$(LOCAL_DIR)/crypto/bio/ossl_core_bio.c \

# Basic hash algorithms
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/sha/sha1dgst.c \
	$(LOCAL_DIR)/crypto/sha/sha1_one.c \
	$(LOCAL_DIR)/crypto/sha/sha256.c \
	$(LOCAL_DIR)/crypto/sha/sha3.c \
	$(LOCAL_DIR)/crypto/sha/sha512.c \
	$(LOCAL_DIR)/crypto/sha/keccak1600.c \

# SM algorithms
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/sm3/legacy_sm3.c \
	$(LOCAL_DIR)/crypto/sm3/sm3.c \
	$(LOCAL_DIR)/crypto/sm4/sm4.c \

# Basic AES
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/aes/aes_cbc.c \
	$(LOCAL_DIR)/crypto/aes/aes_cfb.c \
	$(LOCAL_DIR)/crypto/aes/aes_core.c \
	$(LOCAL_DIR)/crypto/aes/aes_ecb.c \
	$(LOCAL_DIR)/crypto/aes/aes_ige.c \
	$(LOCAL_DIR)/crypto/aes/aes_misc.c \
	$(LOCAL_DIR)/crypto/aes/aes_ofb.c \
	$(LOCAL_DIR)/crypto/aes/aes_wrap.c \

# Basic modes
MODULE_SRCS += \
	$(LOCAL_DIR)/crypto/modes/cbc128.c \
	$(LOCAL_DIR)/crypto/modes/ccm128.c \
	$(LOCAL_DIR)/crypto/modes/cfb128.c \
	$(LOCAL_DIR)/crypto/modes/ctr128.c \
	$(LOCAL_DIR)/crypto/modes/cts128.c \
	$(LOCAL_DIR)/crypto/modes/gcm128.c \
	$(LOCAL_DIR)/crypto/modes/ocb128.c \
	$(LOCAL_DIR)/crypto/modes/ofb128.c \
	$(LOCAL_DIR)/crypto/modes/siv128.c \
	$(LOCAL_DIR)/crypto/modes/wrap128.c \
	$(LOCAL_DIR)/crypto/modes/xts128.c \
